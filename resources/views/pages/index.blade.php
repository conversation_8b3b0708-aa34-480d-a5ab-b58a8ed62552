@extends('layout.index')

@section('content')
    <div class="ck-content">
        <section class="section-hero-1 position-relative pt-200 pb-120 overflow-hidden">
            <div
                class="shape-1 position-relative position-md-absolute bottom-0 start-0 start-md-50 z-1 ms-0 ms-md-10 mb-30 mb-md-0">
                <img src=storage/main/general/hero-man.png data-bb-lazy="true" class="position-relative z-1 filter-gray"
                    loading="lazy" alt="Crafting Intuitive Digital Experiences">
                <div class="position-absolute top-50 start-0 translate-middle z-0 mt-8 ms-10 ps-8"><img
                        src=storage/main/general/hero-decorate.png data-bb-lazy="true" class="ribbonRotate" loading="lazy"
                        alt="Crafting Intuitive Digital Experiences"></div>
            </div>
            <div class="position-absolute top-0 start-0 w-100 h-100 filter-invert"
                data-background="https://zelio.botble.com/storage/main/general/hero-bg.png"></div>
            <div class="container position-relative z-3">
                <div class="row">
                    <div class="col-lg-6 col-md-12"><span class="text-dark">👋 Hi there, I'm Muthee</span>
                        <h1 class="ds-2 mb-3">Crafting Intuitive <span class="text-primary">Digital Experiences</span>
                        </h1>
                        <p class="text-300 mb-6">I assist individuals and brands in achieving their objectives by
                            creating and developing user-focused digital products and interactive experiences.</p><a
                            href="storage/main/resume/cv.pdf" class="btn btn-gradient me-2" target="_blank"> Download CV
                            <svg class="icon ms-2 svg-icon-ti-ti-download" xmlns="http://www.w3.org/2000/svg" width=24
                                height=24 viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2" />
                                <path d="M7 11l5 5l5 -5" />
                                <path d="M12 4l0 12" />
                            </svg></a><a href="#contact"
                            class="btn btn-outline-secondary d-inline-flex align-items-center"><span>Hire Me</span><svg
                                class="icon ms-2 svg-icon-ti-ti-arrow-right" xmlns="http://www.w3.org/2000/svg" width=24
                                height=24 viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path d="M5 12l14 0" />
                                <path d="M13 18l6 -6" />
                                <path d="M13 6l6 6" />
                            </svg></a>
                        <p class="text-400 mt-6 mb-3">+ 12 years with professional design software</p>
                        <div class="d-flex gap-3">
                            <div class="brand-logo icon-xl icon-shape rounded-3 bg-900"><img src=storage/main/skills/1.png
                                    data-bb-lazy="true" loading="lazy" alt="Figma">
                            </div>
                            <div class="brand-logo icon-xl icon-shape rounded-3 bg-900"><img src=storage/main/skills/2.png
                                    data-bb-lazy="true" loading="lazy" alt="Adobe XD">
                            </div>
                            <div class="brand-logo icon-xl icon-shape rounded-3 bg-900"><img src=storage/main/skills/3.png
                                    data-bb-lazy="true" loading="lazy" alt="Illustrator">
                            </div>
                            <div class="brand-logo icon-xl icon-shape rounded-3 bg-900"><img src=storage/main/skills/4.png
                                    data-bb-lazy="true" loading="lazy" alt="Sketch">
                            </div>
                            <div class="brand-logo icon-xl icon-shape rounded-3 bg-900"><img src=storage/main/skills/5.png
                                    data-bb-lazy="true" loading="lazy" alt="Photoshop">
                            </div>
                            <div class="brand-logo icon-xl icon-shape rounded-3 bg-900"><img src=storage/main/skills/6.png
                                    data-bb-lazy="true" loading="lazy" alt="Webflow">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <div class="section-static-1 position-relative overflow-hidden z-0 py-8 bg-900">
            <div class="container">
                <div class="inner">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-lg-auto col-md-6">
                            <div class="counter-item-cover counter-item">
                                <div class="content text-center mx-auto d-flex align-items-center"><span
                                        class="ds-3 count text-primary fw-medium my-0 counter-wrapper"><span
                                            class="plus-icon-counter">+</span><span
                                            class="odometer ds-1 text-dark fw-semibold number-counter">12</span></span>
                                    <div class="text-start ms-2">
                                        <p class="fs-5 mb-0 text-300">Years of</p>
                                        <p class="fs-5 mb-0 fw-bold">Experience</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-auto col-md-6">
                            <div class="counter-item-cover counter-item">
                                <div class="content text-center mx-auto d-flex align-items-center"><span
                                        class="ds-3 count text-primary fw-medium my-0 counter-wrapper"><span
                                            class="plus-icon-counter">+</span><span
                                            class="odometer ds-1 text-dark fw-semibold number-counter">250</span></span>
                                    <div class="text-start ms-2">
                                        <p class="fs-5 mb-0 text-300">Projects</p>
                                        <p class="fs-5 mb-0 fw-bold">Completed</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-auto col-md-6">
                            <div class="counter-item-cover counter-item">
                                <div class="content text-center mx-auto d-flex align-items-center"><span
                                        class="ds-3 count text-primary fw-medium my-0 counter-wrapper"><span
                                            class="plus-icon-counter">+</span><span
                                            class="odometer ds-1 text-dark fw-semibold number-counter">680</span></span>
                                    <div class="text-start ms-2">
                                        <p class="fs-5 mb-0 text-300">Satisfied</p>
                                        <p class="fs-5 mb-0 fw-bold">Happy Clients</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-auto col-md-6">
                            <div class="counter-item-cover counter-item">
                                <div class="content text-center mx-auto d-flex align-items-center"><span
                                        class="ds-3 count text-primary fw-medium my-0 counter-wrapper"><span
                                            class="plus-icon-counter">+</span><span
                                            class="odometer ds-1 text-dark fw-semibold number-counter">18</span></span>
                                    <div class="text-start ms-2">
                                        <p class="fs-5 mb-0 text-300">Awards</p>
                                        <p class="fs-5 mb-0 fw-bold">Won Received</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <style>
            .shortcode-lazy-loading {
                position: relative;
                min-height: 12rem;
            }

            .loading-spinner {
                align-items: center;
                background: hsla(0, 0%, 100%, 0.5);
                display: flex;
                height: 100%;
                inset-inline-start: 0;
                justify-content: center;
                position: absolute;
                top: 0;
                width: 100%;
                z-index: 1;

                &:after {
                    animation: loading-spinner-rotation 0.5s linear infinite;
                    border-color: var(--primary-color) transparent var(--primary-color) transparent;
                    border-radius: 50%;
                    border-style: solid;
                    border-width: 1px;
                    content: ' ';
                    display: block;
                    height: 40px;
                    position: absolute;
                    top: calc(50% - 20px);
                    width: 40px;
                    z-index: 1;
                }
            }

            @keyframes loading-spinner-rotation {
                0% {
                    transform: rotate(0deg);
                }

                100% {
                    transform: rotate(360deg);
                }
            }
        </style>
        <div class="shortcode-lazy-loading" data-name="services"
            data-attributes="{&quot;style&quot;:&quot;1&quot;,&quot;title&quot;:&quot;What do I offer?&quot;,&quot;subtitle&quot;:&quot;My journey started with a fascination for design and technology, leading me to specialize in UI\/UX design&quot;,&quot;service_ids&quot;:&quot;1,4,2,3&quot;}">
            <div class="loading-spinner"></div>
        </div>

        <!-- Updated Services Section -->
        <section class="services-section py-5">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-5">
                        <h2 class="section-title">What do I offer?</h2>
                        <p class="section-subtitle">My journey started with a fascination for design and technology,
                            leading me to specialize in UI/UX design</p>
                    </div>
                </div>
                <div class="row g-4">
                    <div class="col-lg-6 col-md-6">
                        <div class="service-card h-100">
                            <div class="service-number">01.</div>
                            <h3 class="service-title">Web Development</h3>
                            <p class="service-description">Building responsive and modern websites using cutting-edge
                                technologies.</p>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6">
                        <div class="service-card h-100">
                            <div class="service-number">02.</div>
                            <h3 class="service-title">UI/UX Design</h3>
                            <p class="service-description">Creating user-centered designs that provide intuitive and
                                engaging user experiences.</p>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6">
                        <div class="service-card h-100">
                            <div class="service-number">03.</div>
                            <h3 class="service-title">SEO Optimization</h3>
                            <p class="service-description">Improving website rankings on search engines to drive more
                                organic traffic.</p>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6">
                        <div class="service-card h-100">
                            <div class="service-number">04.</div>
                            <h3 class="service-title">Content Creation</h3>
                            <p class="service-description">Providing high-quality content tailored to your business
                                needs.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <div class="shortcode-lazy-loading" data-name="projects"
            data-attributes="{&quot;style&quot;:&quot;1&quot;,&quot;title&quot;:&quot;My Latest Works&quot;,&quot;subtitle&quot;:&quot;I believe that working hard and trying to learn every day will &lt;br&gt; make me improve in satisfying my customers.&quot;,&quot;project_ids&quot;:&quot;1,2,3,4&quot;,&quot;bottom_action_text&quot;:&quot;View all&quot;,&quot;bottom_action_link&quot;:&quot;\/projects&quot;}">
            <div class="loading-spinner"></div>
        </div>
        <div class="shortcode-lazy-loading" data-name="resume"
            data-attributes="{&quot;style&quot;:&quot;1&quot;,&quot;title&quot;:&quot;My Resume&quot;,&quot;subtitle&quot;:&quot;I believe that working hard and trying to learn every day will &lt;br&gt; make me improve in satisfying my customers.&quot;,&quot;action_text&quot;:&quot;Get in touch&quot;,&quot;action_link&quot;:&quot;#contact&quot;,&quot;resume_1_title&quot;:&quot;Education&quot;,&quot;resume_1_title_icon&quot;:&quot;ti ti-school&quot;,&quot;resume_1_quantity&quot;:&quot;4&quot;,&quot;resume_1_time_1&quot;:&quot;2018 - 2019&quot;,&quot;resume_1_title_1&quot;:&quot;Certification in UX Design&quot;,&quot;resume_1_description_1&quot;:&quot;University of Stanford&quot;,&quot;resume_1_subtitle_1&quot;:&quot;4.9\/5&quot;,&quot;resume_1_time_2&quot;:&quot;2017 - 2018&quot;,&quot;resume_1_title_2&quot;:&quot;Certification in Web Dev&quot;,&quot;resume_1_description_2&quot;:&quot;University of Stanford&quot;,&quot;resume_1_subtitle_2&quot;:&quot;5.0\/5&quot;,&quot;resume_1_time_3&quot;:&quot;2014 - 2016&quot;,&quot;resume_1_title_3&quot;:&quot;Advanced UX\/UI Bootcamp&quot;,&quot;resume_1_description_3&quot;:&quot;Design Academy&quot;,&quot;resume_1_subtitle_3&quot;:&quot;4.9\/5&quot;,&quot;resume_1_time_4&quot;:&quot;2012 - 2013&quot;,&quot;resume_1_title_4&quot;:&quot;Certification in Graphic Design&quot;,&quot;resume_1_description_4&quot;:&quot;Coursera&quot;,&quot;resume_1_subtitle_4&quot;:&quot;4.8\/5&quot;,&quot;resume_2_title&quot;:&quot;Experience&quot;,&quot;resume_2_title_icon&quot;:&quot;ti ti-stars&quot;,&quot;resume_2_quantity&quot;:&quot;4&quot;,&quot;resume_2_time_1&quot;:&quot;2019 - Present&quot;,&quot;resume_2_title_1&quot;:&quot;Senior UI\/UX Designer&quot;,&quot;resume_2_description_1&quot;:&quot;Leader in Creative team&quot;,&quot;resume_2_time_2&quot;:&quot;2016 - 2019&quot;,&quot;resume_2_title_2&quot;:&quot;UI\/UX Designer&quot;,&quot;resume_2_description_2&quot;:&quot;Tech Startup&quot;,&quot;resume_2_time_3&quot;:&quot;2014 - 2016&quot;,&quot;resume_2_title_3&quot;:&quot;Freelance UI\/UX Designer&quot;,&quot;resume_2_description_3&quot;:&quot;Self-Employed&quot;,&quot;resume_2_time_4&quot;:&quot;2012 - 2014&quot;,&quot;resume_2_title_4&quot;:&quot;Junior UI Designer&quot;,&quot;resume_2_description_4&quot;:&quot;Web Solutions team&quot;,&quot;bottom_text&quot;:&quot;Branding . Marketing . User Interface&quot;,&quot;background_image&quot;:&quot;main\/general\/grid-bg.png&quot;}">
            <div class="loading-spinner"></div>
        </div>
        <div class="shortcode-lazy-loading" data-name="skills"
            data-attributes="{&quot;style&quot;:&quot;1&quot;,&quot;title&quot;:&quot;My Skills&quot;,&quot;subtitle&quot;:&quot;I thrive on turning complex problems into simple, beautiful &lt;br&gt; solutions that enhance user satisfaction.&quot;,&quot;quantity&quot;:&quot;7&quot;,&quot;name_1&quot;:&quot;Figma&quot;,&quot;image_1&quot;:&quot;main\/skills\/1.png&quot;,&quot;level_1&quot;:&quot;92%&quot;,&quot;name_2&quot;:&quot;Adobe XD&quot;,&quot;image_2&quot;:&quot;main\/skills\/2.png&quot;,&quot;level_2&quot;:&quot;87%&quot;,&quot;name_3&quot;:&quot;Illustrator&quot;,&quot;image_3&quot;:&quot;main\/skills\/3.png&quot;,&quot;level_3&quot;:&quot;82%&quot;,&quot;name_4&quot;:&quot;Sketch&quot;,&quot;image_4&quot;:&quot;main\/skills\/4.png&quot;,&quot;level_4&quot;:&quot;84%&quot;,&quot;name_5&quot;:&quot;Photoshop&quot;,&quot;image_5&quot;:&quot;main\/skills\/5.png&quot;,&quot;level_5&quot;:&quot;86%&quot;,&quot;name_6&quot;:&quot;Webflow&quot;,&quot;image_6&quot;:&quot;main\/skills\/6.png&quot;,&quot;level_6&quot;:&quot;70%&quot;,&quot;name_7&quot;:&quot;Framer&quot;,&quot;image_7&quot;:&quot;main\/skills\/7.png&quot;,&quot;level_7&quot;:&quot;76%&quot;,&quot;extra_content&quot;:&quot;In addition, I have some programming knowledge such as: &lt;br&gt; &lt;span class=&#039;fs-5 fw-bold text-dark&#039;&gt;HTML, CSS, Javascript, Bootstrap, TailwindCSS&lt;\/span&gt;&quot;}">
            <div class="loading-spinner"></div>
        </div>
        <div class="shortcode-lazy-loading" data-name="image-slider"
            data-attributes="{&quot;title&quot;:&quot;Trusted by industry leaders&quot;,&quot;subtitle&quot;:&quot;I have collaborated with many large corporations, companies, and agencies around &lt;br&gt; the world in many fields of design and consulting&quot;,&quot;quantity&quot;:&quot;10&quot;,&quot;name_1&quot;:&quot;Brave&quot;,&quot;image_1&quot;:&quot;main\/brands\/1.png&quot;,&quot;url_1&quot;:&quot;https:\/\/Brave.com&quot;,&quot;open_in_new_tab_1&quot;:&quot;1&quot;,&quot;name_2&quot;:&quot;Circle&quot;,&quot;image_2&quot;:&quot;main\/brands\/2.png&quot;,&quot;url_2&quot;:&quot;https:\/\/Circle.com&quot;,&quot;open_in_new_tab_2&quot;:&quot;1&quot;,&quot;name_3&quot;:&quot;Discord&quot;,&quot;image_3&quot;:&quot;main\/brands\/3.png&quot;,&quot;url_3&quot;:&quot;https:\/\/Discord.com&quot;,&quot;open_in_new_tab_3&quot;:&quot;1&quot;,&quot;name_4&quot;:&quot;Google&quot;,&quot;image_4&quot;:&quot;main\/brands\/4.png&quot;,&quot;url_4&quot;:&quot;https:\/\/Google.com&quot;,&quot;open_in_new_tab_4&quot;:&quot;1&quot;,&quot;name_5&quot;:&quot;jump_&quot;,&quot;image_5&quot;:&quot;main\/brands\/5.png&quot;,&quot;url_5&quot;:&quot;https:\/\/jump.com&quot;,&quot;open_in_new_tab_5&quot;:&quot;1&quot;,&quot;name_6&quot;:&quot;Magic Eden&quot;,&quot;image_6&quot;:&quot;main\/brands\/6.png&quot;,&quot;url_6&quot;:&quot;https:\/\/Magic Eden.com&quot;,&quot;open_in_new_tab_6&quot;:&quot;1&quot;,&quot;name_7&quot;:&quot;MetaMask&quot;,&quot;image_7&quot;:&quot;main\/brands\/7.png&quot;,&quot;url_7&quot;:&quot;https:\/\/MetaMask.com&quot;,&quot;open_in_new_tab_7&quot;:&quot;1&quot;,&quot;name_8&quot;:&quot;Shopify&quot;,&quot;image_8&quot;:&quot;main\/brands\/8.png&quot;,&quot;url_8&quot;:&quot;https:\/\/Shopify.com&quot;,&quot;open_in_new_tab_8&quot;:&quot;1&quot;,&quot;name_9&quot;:&quot;Stripe&quot;,&quot;image_9&quot;:&quot;main\/brands\/9.png&quot;,&quot;url_9&quot;:&quot;https:\/\/Stripe.com&quot;,&quot;open_in_new_tab_9&quot;:&quot;1&quot;,&quot;name_10&quot;:&quot;Lolliapaloza&quot;,&quot;image_10&quot;:&quot;main\/brands\/10.png&quot;,&quot;url_10&quot;:&quot;https:\/\/Lolliapaloza.com&quot;,&quot;open_in_new_tab_10&quot;:&quot;1&quot;}">
            <div class="loading-spinner"></div>
        </div>
        <div class="shortcode-lazy-loading" data-name="testimonials"
            data-attributes="{&quot;title&quot;:&quot;Client&#039;s Testimonials&quot;,&quot;subtitle&quot;:&quot;I believe that working hard and trying to learn every day will make me &lt;br&gt; improve in satisfying my customers.&quot;,&quot;testimonial_ids&quot;:&quot;1,2,3,4&quot;,&quot;shape_image&quot;:&quot;main\/avatars\/man.png&quot;}">
            <div class="loading-spinner"></div>
        </div>
        <div class="shortcode-lazy-loading" data-name="blog-posts"
            data-attributes="{&quot;style&quot;:&quot;1&quot;,&quot;paginate&quot;:&quot;3&quot;,&quot;title&quot;:&quot;Recent blog&quot;,&quot;subtitle&quot;:&quot;Explore the insights and trends shaping our industry&quot;,&quot;action_text&quot;:&quot;View more&quot;,&quot;action_link&quot;:&quot;\/blog&quot;}">
            <div class="loading-spinner"></div>
        </div>
        @include('pages.sections.contact')
    </div>
@endsection
