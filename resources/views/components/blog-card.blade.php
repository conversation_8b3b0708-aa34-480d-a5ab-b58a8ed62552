@props([
    'title',
    'description', 
    'image',
    'category',
    'categoryUrl',
    'url',
    'date',
    'readTime'
])

<div class="col">
    <div class="blog-card rounded-4 mb-lg-3 mb-md-5 mb-3">
        <div class="blog-card__image position-relative">
            <div class="zoom-img rounded-3 overflow-hidden">
                <img src="{{ $image }}" 
                     data-bb-lazy="true" 
                     loading="lazy" 
                     alt="{{ $title }}">
                <a class="position-absolute bottom-0 start-0 m-3 text-white-keep btn btn-gradient fw-medium rounded-3 px-3 py-2"
                   href="{{ $categoryUrl }}">{{ $category }}</a>
                <a href="{{ $url }}"
                   title="{{ $title }}"
                   class="blog-card__link position-absolute top-50 start-50 translate-middle icon-md icon-shape bg-linear-1 rounded-circle">
                    <i class="ri-arrow-right-up-line text-dark"></i>
                </a>
            </div>
        </div>
        <div class="blog-card__content position-relative text-center mt-4">
            <span class="blog-card__date fs-7">{{ $date }} • {{ $readTime }} min read</span>
            <div class="h5 blog-card__title text-truncate">{{ $title }}</div>
            <p class="blog-card__description fs-6">{{ $description }}</p>
            <a href="{{ $url }}"
               class="link-overlay position-absolute top-0 start-0 w-100 h-100"
               title="{{ $title }}"></a>
        </div>
    </div>
</div>
